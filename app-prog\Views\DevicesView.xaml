<UserControl x:Class="NetworkManagement.Views.DevicesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header and Actions in One Row -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,20">
            <StackPanel Margin="20">
                <!-- Title and Search Row -->
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Title Section -->
                    <StackPanel Grid.Column="0" VerticalAlignment="Center" Margin="0,0,20,0">
                        <TextBlock Text="إدارة الأجهزة"
                                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  Margin="0,0,0,2"/>
                        <TextBlock Text="إضافة وتعديل وحذف أجهزة الشبكة • فحص حالة الاتصال"
                                  Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                  Opacity="0.68"/>
                    </StackPanel>

                    <!-- Search Box -->
                    <TextBox Grid.Column="1"
                            materialDesign:HintAssist.Hint="البحث في الأجهزة..."
                            materialDesign:HintAssist.IsFloating="True"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            VerticalAlignment="Center"/>
                </Grid>

                <!-- Action Buttons -->
                <WrapPanel HorizontalAlignment="Right">
                    <Button Command="{Binding AddDeviceCommand}"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Margin="0,0,10,0"
                           Visibility="{Binding CanAddDevices, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="إضافة جهاز"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding PingAllDevicesAsyncCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0"
                           IsEnabled="{Binding IsPinging, Converter={StaticResource InverseBooleanConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Wifi" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="فحص الكل"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding PingSelectedDevicesAsyncCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0"
                           IsEnabled="{Binding IsPinging, Converter={StaticResource InverseBooleanConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="WifiCheck" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="فحص المحدد"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding DeleteSelectedDevicesAsyncCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0"
                           Foreground="Red"
                           Visibility="{Binding CanDeleteDevices, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="حذف المحدد"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding ExportSelectedDevicesAsyncCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Export" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تصدير المحدد"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding SelectAllDevicesCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CheckboxMarkedCircle" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تحديد الكل"/>
                        </StackPanel>
                    </Button>

                    <!-- Selected Count Display -->
                    <Border Background="{DynamicResource PrimaryHueMidBrush}"
                           CornerRadius="12" Padding="8,4"
                           Visibility="{Binding SelectedDevicesCount, Converter={StaticResource ZeroToVisibilityConverter}, ConverterParameter=Inverse}">
                        <TextBlock Text="{Binding SelectedDevicesCount, StringFormat='{}{0} محدد'}"
                                  Foreground="White" FontSize="12" FontWeight="Medium"/>
                    </Border>

                    <Button Command="{Binding ExportDevicesAsyncCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Export" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="تصدير"/>
                        </StackPanel>
                    </Button>

                    <Button Command="{Binding ImportDevicesAsyncCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,10,0"
                           Visibility="{Binding CanAddDevices, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Import" Width="16" Height="16" Margin="0,0,5,0"/>
                            <TextBlock Text="استيراد"/>
                        </StackPanel>
                    </Button>



                    <ToggleButton IsChecked="{Binding AutoRefreshEnabled}"
                                 Command="{Binding ToggleAutoRefreshCommand}"
                                 Style="{StaticResource MaterialDesignActionToggleButton}"
                                 ToolTip="تحديث تلقائي كل 30 ثانية">
                        <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"/>
                    </ToggleButton>
                </WrapPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- Filters -->
        <materialDesign:Card Grid.Row="1" Margin="0,0,0,10">
            <StackPanel Orientation="Horizontal" Margin="20,10">
                <TextBlock Text="فلترة:" VerticalAlignment="Center" Margin="0,0,10,0" FontWeight="Bold"/>

                <ComboBox materialDesign:HintAssist.Hint="الحالة"
                         SelectedValue="{Binding StatusFilter}"
                         SelectedValuePath="Tag"
                         Width="120" Margin="0,0,10,0">
                    <ComboBoxItem Content="الكل" Tag=""/>
                    <ComboBoxItem Content="متصل" Tag="متصل"/>
                    <ComboBoxItem Content="غير متصل" Tag="غير متصل"/>
                    <ComboBoxItem Content="صيانة" Tag="maintenance"/>
                </ComboBox>

                <ComboBox materialDesign:HintAssist.Hint="النوع"
                         SelectedValue="{Binding TypeFilter}"
                         SelectedValuePath="Tag"
                         Width="120" Margin="0,0,10,0">
                    <ComboBoxItem Content="الكل" Tag=""/>
                    <ComboBoxItem Content="راوتر" Tag="راوتر"/>
                    <ComboBoxItem Content="سويتش" Tag="سويتش"/>
                    <ComboBoxItem Content="كاميرا" Tag="كاميرا"/>
                    <ComboBoxItem Content="خادم" Tag="خادم"/>
                </ComboBox>

                <Button Command="{Binding ClearFiltersAsyncCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Content="مسح الفلاتر"
                       Margin="10,0,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Devices List -->
        <materialDesign:Card Grid.Row="2">
            <Grid>
                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                            VerticalAlignment="Top" Height="4"/>

                <!-- Ping Status -->
                <Border Background="{DynamicResource PrimaryHueMidBrush}"
                       Visibility="{Binding IsPinging, Converter={StaticResource BooleanToVisibilityConverter}}"
                       VerticalAlignment="Top" Height="30" Margin="10,10,10,0">
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                        <ProgressBar Width="20" Height="20"
                                   Style="{StaticResource MaterialDesignCircularProgressBar}"
                                   IsIndeterminate="True"
                                   Foreground="White"/>
                        <TextBlock Text="{Binding PingStatus}"
                                 Foreground="White"
                                 VerticalAlignment="Center"
                                 Margin="10,0,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Data Grid -->
                <DataGrid ItemsSource="{Binding Devices}"
                         SelectedItem="{Binding SelectedDevice}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         CanUserSortColumns="True"
                         CanUserReorderColumns="True"
                         CanUserResizeColumns="True"
                         IsReadOnly="True"
                         SelectionMode="Extended"
                         SelectionUnit="FullRow"
                         Margin="10">

                    <DataGrid.Columns>
                        <!-- Selection Column -->
                        <DataGridTemplateColumn Header="تحديد" Width="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                             HorizontalAlignment="Center"
                                             VerticalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 1. عنوان IP -->
                        <DataGridTemplateColumn Header="عنوان IP" Width="130">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="{Binding Ip}"
                                           Command="{Binding DataContext.OpenIpInBrowserCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                           CommandParameter="{Binding Ip}"
                                           Style="{StaticResource MaterialDesignFlatButton}"
                                           Foreground="Blue"
                                           HorizontalAlignment="Left"
                                           Padding="0"
                                           ToolTip="انقر لفتح في المتصفح"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 2. الحالة -->
                        <DataGridTemplateColumn Header="الحالة" Width="90">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding StatusDisplay}">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="متصل">
                                                        <Setter Property="Foreground" Value="Green"/>
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="غير متصل">
                                                        <Setter Property="Foreground" Value="Red"/>
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="active">
                                                        <Setter Property="Foreground" Value="Green"/>
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="inactive">
                                                        <Setter Property="Foreground" Value="Red"/>
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="maintenance">
                                                        <Setter Property="Foreground" Value="Orange"/>
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="disabled">
                                                        <Setter Property="Foreground" Value="Gray"/>
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 3. النوع -->
                        <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="100"/>

                        <!-- 4. المسؤول -->
                        <DataGridTextColumn Header="المسؤول" Binding="{Binding Responsible}" Width="120"/>

                        <!-- 5. رقم الهاتف -->
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="110"/>

                        <!-- 6. الشبكة -->
                        <DataGridTemplateColumn Header="الشبكة" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Network.Name, FallbackValue='غير محدد'}"
                                               ToolTip="{Binding Network.Name, FallbackValue='لم يتم تحديد شبكة لهذا الجهاز'}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 7. آخر فحص -->
                        <DataGridTextColumn Header="آخر فحص" Binding="{Binding LastCheck, StringFormat=dd/MM/yyyy HH:mm}" Width="140"/>

                        <!-- Actions Column -->
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Command="{Binding DataContext.EditDeviceAsyncCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                               CommandParameter="{Binding}"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               ToolTip="تعديل"
                                               Visibility="{Binding Converter={StaticResource CanEditDataConverter}}">
                                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                        </Button>
                                        <Button Command="{Binding DataContext.DeleteDeviceAsyncCommand, RelativeSource={RelativeSource AncestorType={x:Type UserControl}}}"
                                               CommandParameter="{Binding}"
                                               Style="{StaticResource MaterialDesignIconButton}"
                                               ToolTip="حذف"
                                               Visibility="{Binding Converter={StaticResource CanDeleteDataConverter}}">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>